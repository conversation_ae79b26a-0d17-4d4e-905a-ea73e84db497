<template>
  <div id="plant-details">
    <!-- Background layers -->
    <div class="background-container">
      <!-- Background image layer -->
      <div 
        class="background-image" 
        :style="backgroundImageStyle"
        v-if="backgroundImage"
      ></div>
      
      <!-- Gradient overlay -->
      <div 
        class="gradient-overlay" 
        :style="gradientStyle"
      ></div>
    </div>

    <!-- White bars container -->
    <div class="white-bars-container">
      <div 
        v-for="(bar, index) in whiteBars" 
        :key="index"
        class="white-bar"
        :style="getBarStyle(bar)"
      ></div>
    </div>

    <!-- Content -->
    <div class="app-content">
      <header class="header">
          <img src="../assets/agrilife_logo.png" alt="Texas A&M AgriLife Logo">
        </header>

      <main class="content">
        <h1 class="title">{{ plantName }}</h1>
        <h2 class="subtitle">Plant Details</h2>
        
        <!-- Back button -->
        <button class="back-button" @click="$emit('go-back')">
          ← Back to Selection
        </button>
        
        <!-- Plant details content can be added here -->
        <div class="plant-info">
          <p class="plant-description">
            You have selected: <strong>{{ plantName }}</strong>
          </p>
        </div>
      </main>
    </div>
  </div>
</template>

<script>

import backgroundImage from '@/assets/greenhouse-img2.jpg' // Uncomment and update path as needed

export default {
  name: 'PlantDetailsPage',
  props: {
    plantName: {
      type: String,
      required: true
    }
  },
  emits: ['go-back'],
  data() {
    return {
      // Background configuration
      backgroundImage: backgroundImage,
      backgroundImageOpacity: 0.7,
      
      // Gradient configuration
      gradientTopColor: '#08B6E0', // Top color of gradient
      gradientBottomColor: '#05AF6B', // Bottom color of gradient
      gradientOpacity: 0.7, // 0 to 1
      
      // White bars configuration 
      whiteBars: [
        { width: 2216, height: 16, x: -10, y: 175, opacity: 0.8 },
      ]
    };
  },
  computed: {
    backgroundImageStyle() {
      if (!this.backgroundImage) return {};
      
      return {
        backgroundImage: `url(${this.backgroundImage})`,
        opacity: this.backgroundImageOpacity
      };
    },
    
    gradientStyle() {
      const topColor = this.hexToRgba(this.gradientTopColor, this.gradientOpacity);
      const bottomColor = this.hexToRgba(this.gradientBottomColor, this.gradientOpacity);
      
      return {
        background: `linear-gradient(to bottom, ${topColor}, ${bottomColor})`
      };
    }
  },
  methods: {
    // Helper method to convert hex color to rgba with opacity
    hexToRgba(hex, opacity) {
      const r = parseInt(hex.slice(1, 3), 16);
      const g = parseInt(hex.slice(3, 5), 16);
      const b = parseInt(hex.slice(5, 7), 16);
      return `rgba(${r}, ${g}, ${b}, ${opacity})`;
    },
    
    // White bars methods
    getBarStyle(bar) {
      return {
        width: `${bar.width}px`,
        height: `${bar.height}px`,
        left: `${bar.x}px`,
        top: `${bar.y}px`,
        borderRadius: `${bar.height / 2}px`,
        opacity: bar.opacity || 1.0,
      };
    }
  }
}
</script>

<style>
/* Global styles to remove scrollbars */
html, body {
  overflow: hidden;
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
}

/* Hide scrollbars for webkit browsers */
::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbars for Firefox */
html {
  scrollbar-width: none;
}
</style>

<style scoped>
#plant-details {
  font-family: Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: relative;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

/* Background container that covers the entire screen */
.background-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/* Background image layer */
.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

/* Header */
.header {
  position: absolute;
  top: 50px;
  align-items: center;
  z-index: 3;
  transform: scale(1.0);
}

/* Gradient overlay */
.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* White bars styling */
.white-bars-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}

.white-bar {
  position: absolute;
  background-color: white;
  opacity: 0.8;
  transition: all 0.3s ease;
}

/* Content container */
.app-content {
  position: relative;
  z-index: 1;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
}

.content {
  width: 100%;
  max-width: 1200px;
}

.title {
  color: white;
  font-size: 64px;
  margin-top: 200px;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  font-weight: 700;
}

.subtitle {
  color: white;
  font-size: 60px;
  margin-bottom: 10px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  font-weight: 500;
}

.back-button {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 18px;
  cursor: pointer;
  margin: 20px 0;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.plant-info {
  margin-top: 40px;
}

.plant-description {
  color: white;
  font-size: 24px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  margin: 20px 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .title {
    font-size: 48px;
  }
  
  .subtitle {
    font-size: 36px;
  }
  
  .plant-description {
    font-size: 18px;
  }
}
</style>
