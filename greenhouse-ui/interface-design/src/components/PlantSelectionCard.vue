<template>
  <div class="card">
    <h3 class="card-title">{{ title }}</h3>
    <div class="plant-list">
      <button
        v-for="plant in plants"
        :key="plant"
        :class="{ 'selected': selectedPlant === plant }"
        @click="$emit('select-plant', plant)"
      >
        {{ plant }}
      </button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: String,
    plants: Array,
    selectedPlant: String
  },
  emits: ['select-plant']
}
</script>

<style scoped>
.card {
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 10px;
  margin: 10px;
  width: 300px;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 1.2em;
  margin-bottom: 10px;
}

.plant-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

button {
  padding: 10px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1.2em;
  margin-bottom: 10px;
  background-color: #eee;
  transition: background-color 0.3s ease;
}

button.selected {
  background-color: #007bff;
  color: white;
}

button:hover {
  background-color: #ddd;
}

button.selected:hover {
  background-color: #0056b3;
}
</style>