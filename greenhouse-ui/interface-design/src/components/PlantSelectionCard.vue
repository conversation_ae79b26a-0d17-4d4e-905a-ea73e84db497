<template>
  <div class="card">
    <h3 class="card-title">{{ title }}</h3>
    <div class="plant-list">
      <button
        v-for="plant in plants"
        :key="plant.name || plant"
        :class="{
          'selected': selectedPlant === (plant.name || plant),
          'disabled': plant.disabled
        }"
        :disabled="plant.disabled"
        @click="handlePlantClick(plant)"
      >
        {{ plant.name || plant }}
      </button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: String,
    plants: Array, // Can be array of strings or array of objects with {name, disabled}
    selectedPlant: String
  },
  emits: ['select-plant'],
  methods: {
    handlePlantClick(plant) {
      // Don't emit if plant is disabled
      if (plant.disabled) return;

      // Emit the plant name (handle both string and object formats)
      const plantName = plant.name || plant;
      this.$emit('select-plant', plantName);
    }
  }
}
</script>

<style scoped>
.card {
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 10px;
  margin: 10px;
  width: 300px;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 1.2em;
  margin-bottom: 10px;
}

.plant-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

button {
  padding: 12px 16px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1.1em;
  background-color: #eee;
  transition: all 0.3s ease;
  text-align: left;
  width: 100%;
}

button.selected {
  background-color: #007bff;
  color: white;
}

button:hover:not(.disabled) {
  background-color: #ddd;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

button.selected:hover {
  background-color: #0056b3;
}

button.disabled {
  background-color: #f5f5f5;
  color: #999;
  cursor: not-allowed;
  opacity: 0.6;
}

button:disabled {
  cursor: not-allowed;
}
</style>