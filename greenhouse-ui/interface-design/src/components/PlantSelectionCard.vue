<template>
  <div class="card">
    <h1 class="card-title">{{ title }}</h1>
    <div class="title-underline"></div>
    <div class="plant-list">
      <button
        v-for="plant in plants"
        :key="plant.name || plant"
        :class="{
          'selected': selectedPlant === (plant.name || plant),
          'disabled': plant.disabled
        }"
        :disabled="plant.disabled"
        @click="handlePlantClick(plant)"
      >
        {{ plant.name || plant }}
      </button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: String,
    plants: Array, // Can be array of strings or array of objects with {name, disabled}
    selectedPlant: String
  },
  emits: ['select-plant'],
  methods: {
    handlePlantClick(plant) {
      // Don't emit if plant is disabled
      if (plant.disabled) return;

      // Emit the plant name (handle both string and object formats)
      const plantName = plant.name || plant;
      this.$emit('select-plant', plantName);
    }
  }
}
</script>

<style scoped>
.card {
  border-radius: 50px;
  padding: 15px;
  margin: 10px;
  width: 300px;
  box-shadow: 2px 2px 15px rgba(0, 0, 0, 0.15);
  background: #74b596b9;
  display: flex;
  flex-direction:column;
}

.card-title {
  font-size: 48px;
  margin-top: 5px;
  margin-bottom: 15px;
  color: white;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.title-underline {
  width: 80%;
  height: 4px;
  background-color: white;
  border-radius: 2px;
  margin: -15px auto 20px auto;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  opacity: 0.9;
}

.plant-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
  align-items: center;
}

button {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 48px;
  transition: all 0.3s ease;
  text-align: center;
  background-color: rgba(255, 255, 255, 0);
  width: 80%;
  font-weight: 500;
  color: #ffffff;
}

button.selected {
  background: linear-gradient(135deg, #4ffe83 0%, #00f2fe 100%);
  color: rgba(255, 255, 255, 0.226);
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

button:hover:not(.disabled) {
  background-color: rgba(255, 255, 255, 0);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

button.selected:hover {
  background: linear-gradient(135deg, #b4b4b4 0%, #00d4fe 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
}

button.disabled {
  background-color: rgba(255, 255, 255, 0.4);
  color: rgba(255, 255, 255, 0.6);
  cursor: not-allowed;
  opacity: 0.5;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

button:disabled {
  cursor: not-allowed;
}
</style>