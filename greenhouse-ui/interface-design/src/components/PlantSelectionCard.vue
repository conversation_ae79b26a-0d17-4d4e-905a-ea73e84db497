<template>
  <div class="card">
    <h3 class="card-title">{{ title }}</h3>
    <div class="plant-list">
      <button
        v-for="plant in plants"
        :key="plant.name || plant"
        :class="{
          'selected': selectedPlant === (plant.name || plant),
          'disabled': plant.disabled
        }"
        :disabled="plant.disabled"
        @click="handlePlantClick(plant)"
      >
        {{ plant.name || plant }}
      </button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: String,
    plants: Array, // Can be array of strings or array of objects with {name, disabled}
    selectedPlant: String
  },
  emits: ['select-plant'],
  methods: {
    handlePlantClick(plant) {
      // Don't emit if plant is disabled
      if (plant.disabled) return;

      // Emit the plant name (handle both string and object formats)
      const plantName = plant.name || plant;
      this.$emit('select-plant', plantName);
    }
  }
}
</script>

<style scoped>
.card {
  border: 1px solid #ccc;
  border-radius: 12px;
  padding: 20px;
  margin: 10px;
  width: 300px;
  box-shadow: 2px 2px 15px rgba(0, 0, 0, 0.15);
  background: #74B596;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.card-title {
  font-size: 1.4em;
  margin-bottom: 20px;
  color: white;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.plant-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
  align-items: center;
}

button {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1.1em;
  background-color: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
  text-align: center;
  width: 90%;
  font-weight: 500;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #333;
}

button.selected {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

button:hover:not(.disabled) {
  background-color: rgba(255, 255, 255, 1);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

button.selected:hover {
  background: linear-gradient(135deg, #3d8bfe 0%, #00d4fe 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
}

button.disabled {
  background-color: rgba(255, 255, 255, 0.4);
  color: rgba(255, 255, 255, 0.6);
  cursor: not-allowed;
  opacity: 0.5;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

button:disabled {
  cursor: not-allowed;
}
</style>