<template>
  <div id="components-demo">
    <!-- Background layers -->
    <div class="background-container">
      <div 
        class="background-image" 
        :style="backgroundImageStyle"
        v-if="backgroundImage"
      ></div>
      
      <div 
        class="gradient-overlay" 
        :style="gradientStyle"
      ></div>
    </div>

    <!-- Content -->
    <div class="app-content">

      <main class="content">
        <h1 class="title">Component Showcase</h1>
        <h2 class="subtitle">Configurable UI Components</h2>
        
        <!-- Back button -->
        <ConfigurableButton
          text="← Back to Home"
          variant="secondary"
          size="medium"
          @click="goBack"
          class="back-button"
        />
        
        <!-- Button Examples -->
        <section class="demo-section">
          <h3 class="section-title">Configurable Buttons</h3>
          
          <div class="button-grid">
            <ConfigurableButton
              text="Primary Button"
              variant="primary"
              size="medium"
              @click="handleButtonClick('Primary')"
            />
            
            <ConfigurableButton
              text="Secondary"
              variant="secondary"
              size="medium"
              @click="handleButtonClick('Secondary')"
            />
            
            <ConfigurableButton
              text="Success"
              variant="success"
              size="medium"
              @click="handleButtonClick('Success')"
            />
            
            <ConfigurableButton
              text="Danger"
              variant="danger"
              size="medium"
              @click="handleButtonClick('Danger')"
            />
            
            <ConfigurableButton
              text="Large Button"
              variant="primary"
              size="large"
              hover-effect="lift"
              @click="handleButtonClick('Large')"
            />
            
            <ConfigurableButton
              text="Loading..."
              variant="info"
              size="medium"
              :loading="true"
              @click="handleButtonClick('Loading')"
            />
            
            <ConfigurableButton
              text="Disabled"
              variant="warning"
              size="medium"
              :disabled="true"
              @click="handleButtonClick('Disabled')"
            />
            
            <ConfigurableButton
              text="Outline Style"
              variant="outline"
              size="medium"
              hover-effect="glow"
              @click="handleButtonClick('Outline')"
            />
          </div>
        </section>
        
        <!-- Dropdown Examples -->
        <section class="demo-section">
          <h3 class="section-title">Configurable Dropdowns</h3>
          
          <div class="dropdown-grid">
            <div class="dropdown-example">
              <ConfigurableDropdown
                v-model="selectedPlant"
                :options="plantOptions"
                label="Select Plant Type"
                placeholder="Choose a plant..."
                variant="default"
                size="medium"
                @change="handleDropdownChange('plant', $event)"
              />
            </div>
            
            <div class="dropdown-example">
              <ConfigurableDropdown
                v-model="selectedAnalysis"
                :options="analysisOptions"
                label="Analysis Method"
                placeholder="Select analysis..."
                variant="glass"
                size="medium"
                searchable
                @change="handleDropdownChange('analysis', $event)"
              />
            </div>
            
            <div class="dropdown-example">
              <ConfigurableDropdown
                v-model="selectedFeatures"
                :options="featureOptions"
                label="Features (Multiple)"
                placeholder="Select features..."
                variant="outline"
                size="medium"
                :multiple="true"
                searchable
                @change="handleDropdownChange('features', $event)"
              />
            </div>
            
            <div class="dropdown-example">
              <ConfigurableDropdown
                v-model="selectedSize"
                :options="sizeOptions"
                label="Size Options"
                placeholder="Pick a size..."
                variant="filled"
                size="large"
                @change="handleDropdownChange('size', $event)"
              />
            </div>
          </div>
        </section>
        
        <!-- Selection Display -->
        <section class="demo-section">
          <h3 class="section-title">Current Selections</h3>
          <div class="selections-display">
            <div class="selection-item">
              <strong>Plant:</strong> {{ selectedPlant ? getDisplayText(selectedPlant) : 'None' }}
            </div>
            <div class="selection-item">
              <strong>Analysis:</strong> {{ selectedAnalysis ? getDisplayText(selectedAnalysis) : 'None' }}
            </div>
            <div class="selection-item">
              <strong>Features:</strong> {{ getMultipleSelectionText(selectedFeatures) }}
            </div>
            <div class="selection-item">
              <strong>Size:</strong> {{ selectedSize || 'None' }}
            </div>
          </div>
        </section>
      </main>
    </div>
  </div>
</template>

<script>
import ConfigurableButton from '../components/ConfigurableButton.vue'
import ConfigurableDropdown from '../components/ConfigurableDropdown.vue'
import backgroundImage from '@/assets/greenhouse-img2.jpg'

export default {
  name: 'ComponentsDemo',
  components: {
    ConfigurableButton,
    ConfigurableDropdown
  },
  data() {
    return {
      // Background configuration
      backgroundImage: backgroundImage,
      backgroundImageOpacity: 0.7,
      gradientTopColor: '#08B6E0',
      gradientBottomColor: '#05AF6B',
      gradientOpacity: 0.9,
      
      // Form selections
      selectedPlant: null,
      selectedAnalysis: null,
      selectedFeatures: [],
      selectedSize: null,
      
      // Options data
      plantOptions: [
        { label: 'Sorghum', value: 'sorghum' },
        { label: 'Rice', value: 'rice' },
        { label: 'Corn', value: 'corn' },
        { label: 'Wheat', value: 'wheat' },
        { label: 'Barley', value: 'barley' }
      ],
      
      analysisOptions: [
        { label: 'Biotic Stress Analysis', value: 'biotic' },
        { label: 'Abiotic Stress Analysis', value: 'abiotic' },
        { label: 'Growth Rate Analysis', value: 'growth' },
        { label: 'Leaf Area Analysis', value: 'leaf_area' },
        { label: 'Color Analysis', value: 'color' },
        { label: 'Disease Detection', value: 'disease' }
      ],
      
      featureOptions: [
        { label: 'Leaf Count', value: 'leaf_count' },
        { label: 'Plant Height', value: 'height' },
        { label: 'Stem Width', value: 'stem_width' },
        { label: 'Color Distribution', value: 'color_dist' },
        { label: 'Disease Markers', value: 'disease_markers' },
        { label: 'Growth Pattern', value: 'growth_pattern' }
      ],
      
      sizeOptions: ['Small', 'Medium', 'Large', 'Extra Large']
    };
  },
  
  computed: {
    backgroundImageStyle() {
      if (!this.backgroundImage) return {};
      
      return {
        backgroundImage: `url(${this.backgroundImage})`,
        opacity: this.backgroundImageOpacity
      };
    },
    
    gradientStyle() {
      const topColor = this.hexToRgba(this.gradientTopColor, this.gradientOpacity);
      const bottomColor = this.hexToRgba(this.gradientBottomColor, this.gradientOpacity);
      
      return {
        background: `linear-gradient(to bottom, ${topColor}, ${bottomColor})`
      };
    }
  },
  
  methods: {
    goBack() {
      this.$router.push({ name: 'HomePage' });
    },
    
    handleButtonClick(buttonType) {
      console.log(`${buttonType} button clicked!`);
      // You can add specific logic for each button type here
    },
    
    handleDropdownChange(type, value) {
      console.log(`${type} changed to:`, value);
    },
    
    getDisplayText(option) {
      if (!option) return '';
      
      if (typeof option === 'string' || typeof option === 'number') {
        return option.toString();
      }
      
      return option.label || option.toString();
    },
    
    getMultipleSelectionText(selections) {
      if (!selections || selections.length === 0) {
        return 'None';
      }
      
      return selections.map(item => this.getDisplayText(item)).join(', ');
    },
    
    hexToRgba(hex, opacity) {
      const r = parseInt(hex.slice(1, 3), 16);
      const g = parseInt(hex.slice(3, 5), 16);
      const b = parseInt(hex.slice(5, 7), 16);
      return `rgba(${r}, ${g}, ${b}, ${opacity})`;
    }
  }
}
</script>

<style scoped>
#components-demo {
  font-family: Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: relative;
  min-height: 100vh;
  width: 100vw;
  overflow-x: hidden;
}

/* Background styling */
.background-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Content styling */
.app-content {
  position: relative;
  z-index: 1;
  min-height: 100vh;
  padding: 20px;
  text-align: center;
}

.content {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding-top: 0px;
}

.title {
  color: white;
  font-size: 48px;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  font-weight: 700;
}

.subtitle {
  color: white;
  font-size: 24px;
  margin-bottom: 40px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  font-weight: 500;
}

.back-button {
  margin-bottom: 40px;
}

/* Demo sections */
.demo-section {
  margin-bottom: 60px;
  padding: 30px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.section-title {
  color: white;
  font-size: 28px;
  margin-bottom: 30px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Button grid */
.button-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

/* Dropdown grid */
.dropdown-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 20px;
}

.dropdown-example {
  text-align: left;
}

/* Selections display */
.selections-display {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  text-align: left;
  max-width: 600px;
  margin: 0 auto;
}

.selection-item {
  color: white;
  font-size: 16px;
  margin-bottom: 10px;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.selection-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .title {
    font-size: 36px;
  }
  
  .subtitle {
    font-size: 20px;
  }
  
  .button-grid {
    grid-template-columns: 1fr;
  }
  
  .dropdown-grid {
    grid-template-columns: 1fr;
  }
  
  .demo-section {
    padding: 20px;
  }
}
</style>
