<template>
  <div id="plant-details">
    <!-- Background layers -->
    <div class="background-container">
      <!-- Background image layer -->
      <div
        class="background-image"
        :style="backgroundImageStyle"
        v-if="backgroundImage"
      ></div>

      <!-- Gradient overlay -->
      <div
        class="gradient-overlay"
        :style="gradientStyle"
      ></div>
    </div>

    <!-- Content -->
    <div class="app-content">
      <AppHeader :whiteBars="[
        { width: 2216, height: 15, x: -10, y: 175, opacity: 0.8 },
        { width: 15, height: 750, x: 300, y: 200, opacity: 0.8 }]"
      />

      <main class="content">
        <h1 class="title">Plant Details</h1>
        <h2 class="subtitle">{{ plantName }}</h2>

        <!-- Back button -->
        <button class="back-button" @click="goBack">
          ← Back to Selection
        </button>

        <section class = "parameters-section">
          <div class="date-selector">
            <h3 class="section-title">Select Date</h3>
            <ConfigurableDropdown
              v-model="selectedDate"
              :options="dateOptions"
              label="Date"
              placeholder="Select a date..."
              variant="default"
              size="medium"
              @change="handleDateChange"
            />
          </div>
          <div class="plant-id-selector">
            <h3 class="section-title">Select Plant ID</h3>
            <ConfigurableDropdown
              v-model="selectedPlantId"
              :options="plantIdOptions"
              label="Plant ID"
              placeholder="Select a plant ID..."
              variant="default"
              size="medium"
              @change="handlePlantIdChange"
            />
          </div> 

          <div class="analyze-button">
            <h3 class="section-title">Analyze</h3>
            <ConfigurableButton
            text="Analyze"
            variant="primary"
            size="medium"
            @click="handleAnalyzeClick"
          />
          </div>
        </section>
      </main>
      
    </div>
  </div>
</template>

<script>
import AppHeader from '../components/AppHeader.vue'
import backgroundImage from '@/assets/greenhouse-img2.jpg'
import ConfigurableButton from '../components/ConfigurableButton.vue'
import ConfigurableDropdown from '@/components/ConfigurableDropdown.vue';

export default {
  name: 'PlantDetails',
  components: {
    AppHeader,
    ConfigurableDropdown,
    ConfigurableButton
  },
  props: {
    plantName: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      // Background configuration
      backgroundImage: backgroundImage,
      backgroundImageOpacity: 0.7,

      // Gradient configuration
      gradientTopColor: '#08B6E0',
      gradientBottomColor: '#05AF6B',
      gradientOpacity: 0.9,
    };
  },
  computed: {
    backgroundImageStyle() {
      if (!this.backgroundImage) return {};

      return {
        backgroundImage: `url(${this.backgroundImage})`,
        opacity: this.backgroundImageOpacity
      };
    },

    gradientStyle() {
      const topColor = this.hexToRgba(this.gradientTopColor, this.gradientOpacity);
      const bottomColor = this.hexToRgba(this.gradientBottomColor, this.gradientOpacity);

      return {
        background: `linear-gradient(to bottom, ${topColor}, ${bottomColor})`
      };
    }
  },
  methods: {
    goBack() {
      // Navigate back to home page using router
      this.$router.push({ name: 'HomePage' });
    },

    // Helper method to convert hex color to rgba with opacity
    hexToRgba(hex, opacity) {
      const r = parseInt(hex.slice(1, 3), 16);
      const g = parseInt(hex.slice(3, 5), 16);
      const b = parseInt(hex.slice(5, 7), 16);
      return `rgba(${r}, ${g}, ${b}, ${opacity})`;
    }
  }
}
</script>

<style>
/* Global styles to remove scrollbars */
html, body {
  overflow: hidden;
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
}

/* Hide scrollbars for webkit browsers */
::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbars for Firefox */
html {
  scrollbar-width: none;
}
</style>

<style scoped>
#plant-details {
  font-family: Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: relative;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

/* Background container that covers the entire screen */
.background-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/* Background image layer */
.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

/* Gradient overlay */
.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Content container */
.app-content {
  position: relative;
  z-index: 1;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
}

.content {

}

.title {
  color: white;
  font-size: 64px;
  margin-top: 200px;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  font-weight: 700;
}

.subtitle {
  color: white;
  font-size: 60px;
  margin-bottom: 10px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  font-weight: 500;
}

.back-button {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 18px;
  cursor: pointer;
  margin: 20px 0;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.parameters-section {
  display: flo;
  float: left;
  justify-content: start;
  align-items: start;
  /* flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1); */
}

.plant-info {
  margin-top: 40px;
}

.plant-description {
  color: white;
  font-size: 24px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  margin: 20px 0;
}

.details-content {
  color: white;
  font-size: 18px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  max-width: 600px;
  margin: 0 auto;
  text-align: left;
}

.details-content ul {
  margin: 20px 0;
  padding-left: 20px;
}

.details-content li {
  margin: 8px 0;
  line-height: 1.5;
}

/* Responsive design */
@media (max-width: 768px) {
  .title {
    font-size: 48px;
  }

  .subtitle {
    font-size: 36px;
  }

  .plant-description {
    font-size: 18px;
  }

  .details-content {
    font-size: 16px;
    padding: 0 20px;
  }
}
</style>