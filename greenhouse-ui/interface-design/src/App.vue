<template>
  <div id="app" :style="backgroundStyle">
    <!-- <header class="header">
      <img src="/path/to/tees_logo.png" alt="Texas A&M Engineering Experiment Station" class="logo tees-logo">
      <img src="/path/to/agrilife_research_logo.png" alt="Texas A&M AgriLife Research" class="logo agrilife-logo">
      <img src="/path/to/tamu_logo.png" alt="Texas A&M University" class="logo tamu-logo">
      <img src="/path/to/electrical_computer_engineering_logo.png" alt="Electrical & Computer Engineering Texas A&M University" class="logo ece-logo">
    </header> -->

    <main class="content">
      <h1 class="title">Greenhouse Automatic Phenotyping Tool</h1>
      <h2 class="subtitle">Select Plant</h2>

      <div class="selection-container">
        <PlantSelectionCard
          title="Biotic"
          :plants="['Sorghum']"
          @select-plant="handlePlantSelection"
          :selectedPlant="selectedPlant"
        />
        <PlantSelectionCard
          title="Abiotic"
          :plants="['Rice', 'Corn']"
          @select-plant="handlePlantSelection"
          :selectedPlant="selectedPlant"
        />
      </div>
    </main>

    </div>
</template>

<script>
import PlantSelectionCard from './components/PlantSelectionCard.vue'

export default {
  components: {
    PlantSelectionCard
  },
  data() {
    return {
      selectedPlant: ''
    }
  },
  methods: {
    handlePlantSelection(plant) {
      this.selectedPlant = plant
    }
  }
}

</script>

<style scoped>
#app {
  font-family: Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
}

.selection-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: center;
}

</style>