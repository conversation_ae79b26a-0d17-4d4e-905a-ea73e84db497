<template>
  <div id="app">
    <!-- Background layers -->
    <div class="background-container">
      <!-- Background image layer -->
      <div
        class="background-image"
        :style="backgroundImageStyle"
        v-if="backgroundImage"
      ></div>

      <!-- Gradient overlay -->
      <div
        class="gradient-overlay"
        :style="gradientStyle"
      ></div>
    </div>

    <!-- Content -->
    <div class="app-content">
      <!-- <header class="header">
        <img src="/path/to/tees_logo.png" alt="Texas A&M Engineering Experiment Station" class="logo tees-logo">
        <img src="/path/to/agrilife_research_logo.png" alt="Texas A&M AgriLife Research" class="logo agrilife-logo">
        <img src="/path/to/tamu_logo.png" alt="Texas A&M University" class="logo tamu-logo">
        <img src="/path/to/electrical_computer_engineering_logo.png" alt="Electrical & Computer Engineering Texas A&M University" class="logo ece-logo">
      </header> -->

      <main class="content">
        <h1 class="title">Greenhouse Automatic Phenotyping Tool</h1>
        <h2 class="subtitle">Select Plant</h2>

        <div class="selection-container">
          <PlantSelectionCard
            title="Biotic"
            :plants="['Sorghum']"
            @select-plant="handlePlantSelection"
            :selectedPlant="selectedPlant"
          />
          <PlantSelectionCard
            title="Abiotic"
            :plants="['Rice', 'Corn']"
            @select-plant="handlePlantSelection"
            :selectedPlant="selectedPlant"
          />
        </div>
      </main>
    </div>
  </div>
</template>

<script>
import PlantSelectionCard from './components/PlantSelectionCard.vue'
import backgroundImage from '@/assets/greenhouse-img1.jpg' // Uncomment and update path as needed

export default {
  name: 'App',
  components: {
    PlantSelectionCard
  },
  data() {
    return {
      selectedPlant: null,

      // Background configuration
      backgroundImage: backgroundImage, // Set to image URL/path when you want to use an image
      backgroundImageOpacity: 0.6, // 0 to 1

      // Gradient configuration
      gradientTopColor: '#08B6E0', // Top color of gradient
      gradientBottomColor: '#05AF6B', // Bottom color of gradient
      gradientOpacity: 0.7, // 0 to 1
    };
  },
  computed: {
    backgroundImageStyle() {
      if (!this.backgroundImage) return {};

      return {
        backgroundImage: `url(${this.backgroundImage})`,
        opacity: this.backgroundImageOpacity
      };
    },

    gradientStyle() {
      const topColor = this.hexToRgba(this.gradientTopColor, this.gradientOpacity);
      const bottomColor = this.hexToRgba(this.gradientBottomColor, this.gradientOpacity);

      return {
        background: `linear-gradient(to bottom, ${topColor}, ${bottomColor})`
      };
    }
  },
  methods: {
    handlePlantSelection(plant) {
      this.selectedPlant = plant;
    },

    // Helper method to convert hex color to rgba with opacity
    hexToRgba(hex, opacity) {
      const r = parseInt(hex.slice(1, 3), 16);
      const g = parseInt(hex.slice(3, 5), 16);
      const b = parseInt(hex.slice(5, 7), 16);
      return `rgba(${r}, ${g}, ${b}, ${opacity})`;
    },

    // Methods to update background settings
    setBackgroundImage(imageUrl) {
      this.backgroundImage = imageUrl;
    },

    setBackgroundImageOpacity(opacity) {
      this.backgroundImageOpacity = Math.max(0, Math.min(1, opacity));
    },

    setGradientColors(topColor, bottomColor) {
      this.gradientTopColor = topColor;
      this.gradientBottomColor = bottomColor;
    },

    setGradientOpacity(opacity) {
      this.gradientOpacity = Math.max(0, Math.min(1, opacity));
    }
  }
}
</script>

<style scoped>
#app {
  font-family: Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: relative;
  overflow: hidden;
}

/* Background container that covers the entire screen */
.background-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/* Background image layer */
.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

/* Gradient overlay */
.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Content container */
.app-content {
  position: relative;
  z-index: 1;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
}

.content {
  width: 100%;
  max-width: 1200px;
}

.title {
  color: white;
  font-size: 2.5em;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  font-weight: 700;
}

.subtitle {
  color: white;
  font-size: 1.8em;
  margin-bottom: 30px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  font-weight: 500;
}

.selection-container {
  display: flex;
  height: 369px;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: center;
}

/* Responsive design */
@media (max-width: 768px) {
  .title {
    font-size: 2em;
  }

  .subtitle {
    font-size: 1.4em;
  }

  .selection-container {
    flex-direction: column;
    align-items: center;
  }
}
</style>