// set up index.js file for router
import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'HomePage',
    component: () => import('../views/HomePage.vue'),
  },
  {
    path: '/plant-details/:plantName',
    name: 'PlantDetails',
    component: () => import('../views/PlantDetails.vue'),
    props: true
  },
  {
    path: '/components-demo',
    name: 'ComponentsDemo',
    component: () => import('../views/ComponentsDemo.vue')
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

//Automatically go back to home page if user refreshes on plant details page
// router.beforeEach((to, from, next) => {
//   // Check if the route has a name
//   if (from.name === undefined && to.path !== '/') {
    
//     next({path: '/' })
//   }
//   next();
// });

export default router
